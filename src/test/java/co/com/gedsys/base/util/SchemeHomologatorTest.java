package co.com.gedsys.base.util;

import co.com.gedsys.base.adapter.http.gestion_tramite.recepcion.SolicitudRecepcionDocumento;
import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.ValidationException;
import jakarta.validation.Validator;
import jakarta.validation.Validation;
import jakarta.validation.ValidatorFactory;
import jakarta.validation.constraints.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import com.fasterxml.jackson.core.type.TypeReference;

import static org.junit.jupiter.api.Assertions.*;

class SchemeHomologatorTest {

    private ObjectMapper objectMapper;
    private Validator validator;

    @BeforeEach
    void setUp() {
        this.objectMapper = new ObjectMapper();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        this.validator = factory.getValidator();
    }

    private record SampleSchema(
            @NotNull String requiredField,
            @Size(min = 3, max = 10) String sizeField,
            @Min(1) int minField,
            @NotEmpty List<@NotBlank String> nonEmptyList
    ) {
    }

    @Test
    public void testValidateAndConvertToSchema_ValidInput() {
        SchemeHomologator<SampleSchema> homologator = new SchemeHomologator<>(SampleSchema.class);

        Map<String, Object> input = new HashMap<>();
        input.put("requiredField", "Some Value");
        input.put("sizeField", "Length");
        input.put("minField", 5);
        input.put("nonEmptyList", List.of("item1", "item2"));

        SampleSchema result = homologator.validateAndConvertToSchema(input);

        assertNotNull(result);
        assertEquals("Some Value", result.requiredField());
        assertEquals("Length", result.sizeField());
        assertEquals(5, result.minField());
        assertEquals(List.of("item1", "item2"), result.nonEmptyList());
    }

    @Test
    public void testValidateAndConvertToSchema_MissingRequiredField() {
        SchemeHomologator<SampleSchema> homologator = new SchemeHomologator<>(SampleSchema.class);

        Map<String, Object> input = new HashMap<>();
        input.put("sizeField", "Length");
        input.put("minField", 5);
        input.put("nonEmptyList", List.of("item1", "item2"));

        ValidationException exception = assertThrows(ValidationException.class, () -> homologator.validateAndConvertToSchema(input));
        assertTrue(exception.getMessage().contains("requiredField"));
    }

    @Test
    public void testValidateAndConvertToSchema_InvalidSizeField() {
        SchemeHomologator<SampleSchema> homologator = new SchemeHomologator<>(SampleSchema.class);

        Map<String, Object> input = new HashMap<>();
        input.put("requiredField", "Value");
        input.put("sizeField", "Too Long Value");
        input.put("minField", 5);
        input.put("nonEmptyList", List.of("item1", "item2"));

        ValidationException exception = assertThrows(ValidationException.class, () -> homologator.validateAndConvertToSchema(input));
        assertTrue(exception.getMessage().contains("sizeField"));
    }

    @Test
    public void testValidateAndConvertToSchema_InvalidMinField() {
        SchemeHomologator<SampleSchema> homologator = new SchemeHomologator<>(SampleSchema.class);

        Map<String, Object> input = new HashMap<>();
        input.put("requiredField", "Value");
        input.put("sizeField", "Valid");
        input.put("minField", 0);
        input.put("nonEmptyList", List.of("item1", "item2"));

        ValidationException exception = assertThrows(ValidationException.class, () -> homologator.validateAndConvertToSchema(input));
        assertTrue(exception.getMessage().contains("minField"));
    }

    @Test
    public void testValidateAndConvertToSchema_EmptyList() {
        SchemeHomologator<SampleSchema> homologator = new SchemeHomologator<>(SampleSchema.class);

        Map<String, Object> input = new HashMap<>();
        input.put("requiredField", "Value");
        input.put("sizeField", "Valid");
        input.put("minField", 5);
        input.put("nonEmptyList", List.of());

        ValidationException exception = assertThrows(ValidationException.class, () -> homologator.validateAndConvertToSchema(input));
        assertTrue(exception.getMessage().contains("nonEmptyList"));
    }

    @Test
    public void testSolicitudRecepcionDocumento_ConDatosAnidados() {
        SchemeHomologator<SolicitudRecepcionDocumento> homologator = new SchemeHomologator<>(SolicitudRecepcionDocumento.class);

        Map<String, Object> input = crearJsonDePrueba();

        SolicitudRecepcionDocumento result = homologator.validateAndConvertToSchema(input);

        assertNotNull(result);
        assertEquals("Informe de Actividades Q3", result.titulo());
        assertEquals("9f1c3de8-b12a-4b0e-9c1e-2fce6e7e5b5b", result.fileId());
        assertEquals("169c4894-637f-4c0c-83a5-7d494841eb73", result.tipoDocumentalId());
        assertEquals("0d405a15-11a2-426a-89ca-7aede3280887", result.unidadDocumentalId());
        assertEquals("jmarin", result.autor());
        assertEquals("c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d", result.remitenteId());
        assertEquals("emontoya", result.destinatarioInterno());

        assertNotNull(result.propiedadesRadicado());
        assertEquals(200, result.propiedadesRadicado().height());
        assertEquals(1, result.propiedadesRadicado().page());
        assertEquals(300, result.propiedadesRadicado().width());
        assertEquals(50, result.propiedadesRadicado().x());
        assertEquals(100, result.propiedadesRadicado().y());
        assertEquals(0, result.propiedadesRadicado().rotationDegrees());

        assertNotNull(result.metadatos());
        assertEquals(1, result.metadatos().size());
        assertEquals("folios", result.metadatos().get(0).nombre());
        assertEquals("1", result.metadatos().get(0).valor());
        assertEquals(TipoMetadatoEnum.CONTENIDO, result.metadatos().get(0).tipo());

        assertNotNull(result.anexos());
        assertEquals(1, result.anexos().size());
        assertEquals("Balance_Q2", result.anexos().get(0).nombre());
        assertEquals("Balance financiero del segundo trimestre", result.anexos().get(0).descripcion());
        assertEquals("a7425c3f-4d8b-4219-842f-eeb9e8dc9ab3", result.anexos().get(0).fileId());
        assertEquals("5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8", result.anexos().get(0).hash());
        assertEquals(204800L, result.anexos().get(0).bytes());
        assertEquals("pdf", result.anexos().get(0).extension());
    }

    @Test
    public void testSolicitudRecepcionDocumento_ConDatosDesordenados() {
        SchemeHomologator<SolicitudRecepcionDocumento> homologator = new SchemeHomologator<>(SolicitudRecepcionDocumento.class);

        Map<String, Object> input = new HashMap<>();
        input.put("destinatarioInterno", "usuario.interno");
        input.put("remitenteId", "remitente-123");
        input.put("anexos", List.of(Map.of(
                "extension", "docx",
                "bytes", 1024L,
                "hash", "hash123",
                "fileId", "file-456",
                "descripcion", "Anexo de prueba",
                "nombre", "Documento"
        )));
        input.put("propiedadesRadicado", Map.of(
                "rotationDegrees", 90,
                "y", 200,
                "x", 150,
                "width", 400,
                "page", 2,
                "height", 300
        ));
        input.put("metadatos", List.of(Map.of(
                "tipo", "CONTENIDO",
                "valor", "5",
                "nombre", "paginas"
        )));
        input.put("autor", "autor.test");
        input.put("unidadDocumentalId", "unidad-789");
        input.put("tipoDocumentalId", "tipo-456");
        input.put("fileId", "archivo-123");
        input.put("titulo", "Documento de Prueba");

        SolicitudRecepcionDocumento result = homologator.validateAndConvertToSchema(input);

        assertNotNull(result);
        assertEquals("Documento de Prueba", result.titulo());
        assertEquals("archivo-123", result.fileId());
        assertEquals("tipo-456", result.tipoDocumentalId());
        assertEquals("unidad-789", result.unidadDocumentalId());
        assertEquals("autor.test", result.autor());
        assertEquals("remitente-123", result.remitenteId());
        assertEquals("usuario.interno", result.destinatarioInterno());
    }

    @Test
    public void testSolicitudRecepcionDocumento_CamposRequeridos() {
        SchemeHomologator<SolicitudRecepcionDocumento> homologator = new SchemeHomologator<>(SolicitudRecepcionDocumento.class);

        Map<String, Object> input = new HashMap<>();
        input.put("titulo", "");
        input.put("fileId", "file-123");
        input.put("tipoDocumentalId", "tipo-123");
        input.put("unidadDocumentalId", "unidad-123");
        input.put("autor", "autor");
        input.put("remitenteId", "remitente-123");
        input.put("propiedadesRadicado", Map.of(
                "height", 100, "page", 1, "width", 200, "x", 50, "y", 50, "rotationDegrees", 0
        ));

        ValidationException exception = assertThrows(ValidationException.class,
                () -> homologator.validateAndConvertToSchema(input));
        assertTrue(exception.getMessage().contains("titulo"));
    }

    private Map<String, Object> crearJsonDePrueba() {
        Map<String, Object> variables = new HashMap<>();
        variables.put("autor", "dplata");
        variables.put("titulo", "Informe de Actividades Q3");
        variables.put("fileId", "9f1c3de8-b12a-4b0e-9c1e-2fce6e7e5b5b");
        variables.put("tipoDocumentalId", "169c4894-637f-4c0c-83a5-7d494841eb73");
        variables.put("unidadDocumentalId", "0d405a15-11a2-426a-89ca-7aede3280887");
        variables.put("autor", "jmarin");
        variables.put("metadatos", List.of(Map.of(
                "nombre", "folios",
                "valor", "1",
                "tipo", "CONTENIDO"
        )));
        variables.put("propiedadesRadicado", Map.of(
                "height", 200,
                "page", 1,
                "width", 300,
                "x", 50,
                "y", 100,
                "rotationDegrees", 0
        ));
        variables.put("anexos", List.of(Map.of(
                "nombre", "Balance_Q2",
                "descripcion", "Balance financiero del segundo trimestre",
                "fileId", "a7425c3f-4d8b-4219-842f-eeb9e8dc9ab3",
                "hash", "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8",
                "bytes", 204800L,
                "extension", "pdf"
        )));
        variables.put("remitenteId", "c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d");
        variables.put("destinatarioInterno", "emontoya");

        Map<String, Object> input = new HashMap<>();
        input.put("variables", variables);
        return input;
    }

    @Test
    public void testSolicitudRecepcionDocumento_DirectFields() {
        SchemeHomologator<SolicitudRecepcionDocumento> homologator = new SchemeHomologator<>(SolicitudRecepcionDocumento.class);

        Map<String, Object> input = new HashMap<>();
        input.put("titulo", "Informe de Actividades Q3");
        input.put("fileId", "9f1c3de8-b12a-4b0e-9c1e-2fce6e7e5b5b");
        input.put("tipoDocumentalId", "169c4894-637f-4c0c-83a5-7d494841eb73");
        input.put("unidadDocumentalId", "0d405a15-11a2-426a-89ca-7aede3280887");
        input.put("autor", "jmarin");
        input.put("remitenteId", "c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d");
        input.put("destinatarioInterno", "emontoya");
        input.put("metadatos", List.of(Map.of(
                "nombre", "folios",
                "valor", "1",
                "tipo", "CONTENIDO"
        )));
        input.put("propiedadesRadicado", Map.of(
                "height", 200,
                "page", 1,
                "width", 300,
                "x", 50,
                "y", 100,
                "rotationDegrees", 0
        ));
        input.put("anexos", List.of(Map.of(
                "nombre", "Balance_Q2",
                "descripcion", "Balance financiero del segundo trimestre",
                "fileId", "a7425c3f-4d8b-4219-842f-eeb9e8dc9ab3",
                "hash", "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8",
                "bytes", 204800L,
                "extension", "pdf"
        )));

        SolicitudRecepcionDocumento result = homologator.validateAndConvertToSchema(input);

        assertNotNull(result);
        assertEquals("Informe de Actividades Q3", result.titulo());
        assertEquals("jmarin", result.autor());
        assertEquals("c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d", result.remitenteId());
    }

    @Test
    public void testSolicitudEnvioDocumento_ConEstructuraAnidadaCompleja() {
        // Importar la clase SolicitudEnvioDocumento
        SchemeHomologator<co.com.gedsys.base.adapter.http.gestion_tramite.envio.SolicitudEnvioDocumento> homologator =
            new SchemeHomologator<>(co.com.gedsys.base.adapter.http.gestion_tramite.envio.SolicitudEnvioDocumento.class);

        // JSON exacto del problema reportado por el usuario
        Map<String, Object> input = new HashMap<>();
        input.put("documentId", "84f92641-5d5b-43c8-9c03-f38db4a61904");
        input.put("emisorUsername", "dplata");

        Map<String, Object> receivedData = new HashMap<>();
        receivedData.put("emisorUsername", "dplata");

        Map<String, Object> formularioEstampado = new HashMap<>();
        Map<String, Object> propiedadesRadicado = new HashMap<>();
        propiedadesRadicado.put("page", 1);
        propiedadesRadicado.put("x", 74.25);
        propiedadesRadicado.put("y", 189);
        propiedadesRadicado.put("height", 131.7900403768506);
        propiedadesRadicado.put("width", 489.6);
        propiedadesRadicado.put("rotationDegrees", 0);
        formularioEstampado.put("propiedadesRadicado", propiedadesRadicado);

        Map<String, Object> formularioCarga = new HashMap<>();
        formularioCarga.put("titulo", "envio proof 4");
        formularioCarga.put("unidadDocumentalId", "e50f0f10-a85d-416f-8ce4-a9fddee0793f");
        formularioCarga.put("seccionId", "165c3816-67ca-471a-a146-642a51c3e287");
        formularioCarga.put("destinatarioId", "75c2f12e-d19c-4fb6-841b-41092af9596c");
        formularioCarga.put("copiasAExternos", List.of("ca89103f-35f6-4ffe-807e-1813f283f391"));
        formularioCarga.put("fileId", "21ce0fea-3a29-4e99-a3b4-fdedd9b61241");
        formularioCarga.put("tipoDocumentalId", "08ad69ef-c4ac-4e43-be5d-7cea80177091");
        formularioCarga.put("metadatos", List.of(Map.of(
                "nombre", "asunto",
                "valor", "asunto4",
                "tipo", "CONTENIDO"
        )));
        formularioCarga.put("anexos", List.of());

        receivedData.put("_formularioEstampado", formularioEstampado);
        receivedData.put("_formularioCarga", formularioCarga);
        receivedData.put("autor", "dplata");

        input.put("receivedData", receivedData);

        // Este test debería reproducir el problema: propiedadesRadicado queda vacío
        co.com.gedsys.base.adapter.http.gestion_tramite.envio.SolicitudEnvioDocumento result =
            homologator.validateAndConvertToSchema(input);

        assertNotNull(result);
        assertEquals("84f92641-5d5b-43c8-9c03-f38db4a61904", result.documentId().toString());
        assertEquals("dplata", result.emisorUsername());

        // Debug: Imprimir el resultado para ver qué está pasando
        System.out.println("=== DEBUG INFO ===");
        System.out.println("propiedadesRadicado: " + result.propiedadesRadicado());
        if (result.propiedadesRadicado() != null) {
            System.out.println("page: " + result.propiedadesRadicado().page());
            System.out.println("x: " + result.propiedadesRadicado().x());
            System.out.println("y: " + result.propiedadesRadicado().y());
            System.out.println("height: " + result.propiedadesRadicado().height());
            System.out.println("width: " + result.propiedadesRadicado().width());
            System.out.println("rotationDegrees: " + result.propiedadesRadicado().rotationDegrees());
        }
        System.out.println("destinatarioId: " + result.destinatarioId());
        System.out.println("metadatos: " + result.metadatos());
        System.out.println("==================");

        // Verificar que propiedadesRadicado se mapee correctamente desde la estructura anidada
        assertNotNull(result.propiedadesRadicado(), "propiedadesRadicado no debería ser null");
        assertEquals(1.0, result.propiedadesRadicado().page(), 0.001, "page debería ser 1.0");
        assertEquals(74.25, result.propiedadesRadicado().x(), 0.001, "x debería ser 74.25 (manteniendo precisión decimal)");
        assertEquals(189.0, result.propiedadesRadicado().y(), 0.001, "y debería ser 189.0");
        assertEquals(131.79, result.propiedadesRadicado().height(), 0.001, "height debería ser 131.79 (manteniendo precisión decimal)");
        assertEquals(489.6, result.propiedadesRadicado().width(), 0.001, "width debería ser 489.6 (manteniendo precisión decimal)");
        assertEquals(0.0, result.propiedadesRadicado().rotationDegrees(), 0.001, "rotationDegrees debería ser 0.0");
    }

    @Test
    public void testSolicitudEnvioDocumento_ConJSONExactoDelProblema() throws Exception {
        // Usar ObjectMapper para simular exactamente lo que hace el controlador
        ObjectMapper objectMapper = new ObjectMapper();

        // JSON exacto del problema reportado
        String jsonExacto = """
            {
                "documentId": "84f92641-5d5b-43c8-9c03-f38db4a61904",
                "emisorUsername": "dplata",
                "receivedData": {
                    "emisorUsername": "dplata",
                    "_formularioEstampado": {
                        "propiedadesRadicado": {
                            "page": 1,
                            "x": 74.25,
                            "y": 189,
                            "height": 131.7900403768506,
                            "width": 489.6,
                            "rotationDegrees": 0
                        }
                    },
                    "_formularioCarga": {
                        "titulo": "envio proof 4",
                        "unidadDocumentalId": "e50f0f10-a85d-416f-8ce4-a9fddee0793f",
                        "seccionId": "165c3816-67ca-471a-a146-642a51c3e287",
                        "destinatarioId": "75c2f12e-d19c-4fb6-841b-41092af9596c",
                        "copiasAExternos": [
                            "ca89103f-35f6-4ffe-807e-1813f283f391"
                        ],
                        "fileId": "21ce0fea-3a29-4e99-a3b4-fdedd9b61241",
                        "tipoDocumentalId": "08ad69ef-c4ac-4e43-be5d-7cea80177091",
                        "metadatos": [
                            {
                                "nombre": "asunto",
                                "valor": "asunto4",
                                "tipo": "CONTENIDO"
                            }
                        ],
                        "anexos": []
                    },
                    "autor": "dplata"
                }
            }
            """;

        // Convertir JSON a Map como lo hace Spring Boot
        Map<String, Object> request = objectMapper.readValue(jsonExacto, new TypeReference<Map<String, Object>>() {});

        // Aplicar SchemeHomologator como lo hace el controlador
        SchemeHomologator<co.com.gedsys.base.adapter.http.gestion_tramite.envio.SolicitudEnvioDocumento> homologator =
            new SchemeHomologator<>(co.com.gedsys.base.adapter.http.gestion_tramite.envio.SolicitudEnvioDocumento.class);

        co.com.gedsys.base.adapter.http.gestion_tramite.envio.SolicitudEnvioDocumento result =
            homologator.validateAndConvertToSchema(request);

        // Debug detallado
        System.out.println("=== RESULTADO FINAL ===");
        System.out.println("documentId: " + result.documentId());
        System.out.println("emisorUsername: " + result.emisorUsername());
        System.out.println("destinatarioId: " + result.destinatarioId());
        System.out.println("propiedadesRadicado: " + result.propiedadesRadicado());
        if (result.propiedadesRadicado() != null) {
            System.out.println("  page: " + result.propiedadesRadicado().page());
            System.out.println("  x: " + result.propiedadesRadicado().x());
            System.out.println("  y: " + result.propiedadesRadicado().y());
            System.out.println("  height: " + result.propiedadesRadicado().height());
            System.out.println("  width: " + result.propiedadesRadicado().width());
            System.out.println("  rotationDegrees: " + result.propiedadesRadicado().rotationDegrees());
        }
        System.out.println("metadatos: " + result.metadatos());
        System.out.println("=======================");

        // Verificaciones
        assertNotNull(result);
        assertEquals("84f92641-5d5b-43c8-9c03-f38db4a61904", result.documentId().toString());
        assertEquals("dplata", result.emisorUsername());

        // Esta es la verificación clave: ¿propiedadesRadicado se mapea correctamente?
        // Ahora preserva la precisión decimal del JSON de entrada
        assertNotNull(result.propiedadesRadicado(), "propiedadesRadicado NO debería ser null");
        assertEquals(1.0, result.propiedadesRadicado().page(), 0.001);
        assertEquals(74.25, result.propiedadesRadicado().x(), 0.001);
        assertEquals(189.0, result.propiedadesRadicado().y(), 0.001);
        assertEquals(131.7900403768506, result.propiedadesRadicado().height(), 0.001);
        assertEquals(489.6, result.propiedadesRadicado().width(), 0.001);
        assertEquals(0.0, result.propiedadesRadicado().rotationDegrees(), 0.001);
    }

    @Test
    public void testSolicitudEnvioDocumento_VerificarMapeadorCompleto() {
        // Test para verificar que el mapeador funciona correctamente
        co.com.gedsys.base.adapter.http.gestion_tramite.envio.AdapterMapperEnvioDocumental mapper =
            org.mapstruct.factory.Mappers.getMapper(co.com.gedsys.base.adapter.http.gestion_tramite.envio.AdapterMapperEnvioDocumental.class);

        // Crear SolicitudEnvioDocumento con propiedades
        co.com.gedsys.base.adapter.http.produccion.documentos.EstructuraPropiedadesRadicado propiedades =
            new co.com.gedsys.base.adapter.http.produccion.documentos.EstructuraPropiedadesRadicado(
                131.0, 1.0, 489.0, 74.0, 189.0, 0.0);

        co.com.gedsys.base.adapter.http.gestion_tramite.envio.SolicitudEnvioDocumento solicitud =
            new co.com.gedsys.base.adapter.http.gestion_tramite.envio.SolicitudEnvioDocumento(
                UUID.fromString("84f92641-5d5b-43c8-9c03-f38db4a61904"),
                "dplata",
                UUID.fromString("75c2f12e-d19c-4fb6-841b-41092af9596c"),
                List.of(),
                propiedades);

        // Mapear a comando
        co.com.gedsys.base.application.usecase.gestion_tramite.envio_documental.RadicarDocumentoEnvioCommand command =
            mapper.toRadicarDocumentoEnvioCommand(solicitud);

        // Verificar que las propiedades se mapean correctamente
        assertNotNull(command.propiedadRadicado(), "propiedadRadicado no debería ser null en el comando");
        assertEquals(131.0, command.propiedadRadicado().height(), 0.001);
        assertEquals(1.0, command.propiedadRadicado().page(), 0.001);
        assertEquals(489.0, command.propiedadRadicado().width(), 0.001);
        assertEquals(74.0, command.propiedadRadicado().x(), 0.001);
        assertEquals(189.0, command.propiedadRadicado().y(), 0.001);
        assertEquals(0.0, command.propiedadRadicado().rotationDegrees(), 0.001);

        System.out.println("=== VERIFICACIÓN MAPEADOR ===");
        System.out.println("Command propiedadRadicado: " + command.propiedadRadicado());
        System.out.println("==============================");
    }
}