package co.com.gedsys.base.util;

import co.com.gedsys.base.adapter.http.gestion_tramite.envio.SolicitudEnvioDocumento;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.ValidationException;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

public class SchemeHomologatorIntegrationTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testCurlRequest_SinPropiedadesRadicado_DeberiaFallar() throws Exception {
        // Simular el JSON del curl request original
        String jsonRequest = """
            {
                "documentId":"12fa287e-7076-4fb2-b71a-5e8fefc7123e",
                "emisorUsername": "dplata",
                "destinatarioId": "c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d"
            }
            """;

        Map<String, Object> requestMap = objectMapper.readValue(jsonRequest, new TypeReference<Map<String, Object>>() {});

        SchemeHomologator<SolicitudEnvioDocumento> homologator = 
            new SchemeHomologator<>(SolicitudEnvioDocumento.class);

        // Esto debería fallar ahora con la validación correcta
        ValidationException exception = assertThrows(ValidationException.class, () -> {
            homologator.validateAndConvertToSchema(requestMap);
        });

        System.out.println("Mensaje de error: " + exception.getMessage());
        
        // Verificar que el error menciona las propiedades del radicado
        assertTrue(exception.getMessage().contains("propiedadesRadicado"));
        assertTrue(exception.getMessage().contains("requeridas") || exception.getMessage().contains("required"));
    }

    @Test
    public void testCurlRequest_ConPropiedadesRadicado_DeberiaFuncionar() throws Exception {
        // Simular el JSON con propiedades del radicado
        String jsonRequest = """
            {
                "documentId":"12fa287e-7076-4fb2-b71a-5e8fefc7123e",
                "emisorUsername": "dplata",
                "destinatarioId": "c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d",
                "propiedadesRadicado": {
                    "height": 100.5,
                    "page": 1.0,
                    "width": 200.25,
                    "x": 50.75,
                    "y": 75.125,
                    "rotationDegrees": 0.0
                }
            }
            """;

        Map<String, Object> requestMap = objectMapper.readValue(jsonRequest, new TypeReference<Map<String, Object>>() {});

        SchemeHomologator<SolicitudEnvioDocumento> homologator = 
            new SchemeHomologator<>(SolicitudEnvioDocumento.class);

        // Esto debería funcionar correctamente
        assertDoesNotThrow(() -> {
            SolicitudEnvioDocumento result = homologator.validateAndConvertToSchema(requestMap);
            assertNotNull(result);
            assertNotNull(result.propiedadesRadicado());
            assertEquals(100.5, result.propiedadesRadicado().height(), 0.001);
            assertEquals(1.0, result.propiedadesRadicado().page(), 0.001);
            assertEquals(200.25, result.propiedadesRadicado().width(), 0.001);
            assertEquals(50.75, result.propiedadesRadicado().x(), 0.001);
            assertEquals(75.125, result.propiedadesRadicado().y(), 0.001);
            assertEquals(0.0, result.propiedadesRadicado().rotationDegrees(), 0.001);
        });
    }
}
