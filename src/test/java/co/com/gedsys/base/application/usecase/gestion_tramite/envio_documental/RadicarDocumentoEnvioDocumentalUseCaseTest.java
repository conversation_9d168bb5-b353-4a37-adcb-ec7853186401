package co.com.gedsys.base.application.usecase.gestion_tramite.envio_documental;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.dto.MetadatoDocumentoDTO;
import co.com.gedsys.base.application.dto.PropiedadRadicadoDTO;
import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.mapper.RadicadoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.gestion_tramite.envio_documental.exception.MetadatoNoExisteException;
import co.com.gedsys.base.application.usecase.gestion_tramite.envio_documental.exception.PoliticaUnicoRadicadoDeEnvioException;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.metadato.DefinicionMetadato;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.radicado.RadicadoRepository;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserStatus;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import co.com.gedsys.base.domain.metadato.enums.FormatoMetadatoEnum;
import co.com.gedsys.base.domain.organizacion.UsuarioSeccion;
import co.com.gedsys.base.domain.organizacion.TipoRelacionUsuarioSeccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import co.com.gedsys.base.domain.metadato.Metadato;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class RadicarDocumentoEnvioDocumentalUseCaseTest {

    @Mock
    private DocumentoRepository documentoRepository;
    @Mock
    private ConsecutivoRepository consecutivoRepository;
    @Mock
    private RadicadoRepository radicadoRepository;
    @Mock
    private ExternalUsersRepository externalUsersRepository;
    @Mock
    private DefinicionMetadatosRepository definicionMetadatosRepository;
    @Mock
    private SeccionRepository seccionRepository;
    @Mock
    private RadicadoApplicationLayerMapper radicadoMapper;

    @InjectMocks
    private RadicarDocumentoEnvioDocumentalUseCase useCase;

    @BeforeEach
    void setUp() {
        // Inicialización común solo si es estrictamente necesaria
    }

    @Test
    @DisplayName("Debe generar un nuevo radicado de envío correctamente")
    void deberiaCrearUnRadicado() {
        UUID documentoId = UUID.randomUUID();
        UUID destinatarioId = UUID.randomUUID();
        List<MetadatoDocumentoDTO> metadatos = List.of();
        RadicarDocumentoEnvioCommand command = new RadicarDocumentoEnvioCommand(documentoId, destinatarioId, "usuario.test", metadatos, null);
        Documento documento = mock(Documento.class);
        Consecutivo consecutivo = mock(Consecutivo.class);
        when(consecutivo.getTipoConsecutivo()).thenReturn(co.com.gedsys.base.domain.consecutivo.TipoConsecutivo.ENVIO);
        ExternalUser destinatario = mock(ExternalUser.class);
        when(destinatario.getStatus()).thenReturn(co.com.gedsys.base.domain.usuario_externo.ExternalUserStatus.ACTIVO);

        // Configurar mocks
        when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documento));
        when(documento.getRadicados()).thenReturn(new ArrayList<>());
        when(consecutivoRepository.findByExample(any())).thenReturn(Optional.of(consecutivo));
        when(externalUsersRepository.findById(destinatarioId)).thenReturn(Optional.of(destinatario));
        when(definicionMetadatosRepository.buscarPorPatrones(any())).thenReturn(Set.of());

        // Mock para UsuarioSeccion con relación primaria
        UsuarioSeccion usuarioSeccion = mock(UsuarioSeccion.class);
        when(usuarioSeccion.getUsername()).thenReturn("usuario.test");
        when(usuarioSeccion.getRelacion()).thenReturn(TipoRelacionUsuarioSeccion.PRIMARIA);

        // Mock para Seccion que contiene el usuario
        co.com.gedsys.base.domain.organizacion.Seccion seccion = mock(co.com.gedsys.base.domain.organizacion.Seccion.class);
        when(seccion.getUsuarios()).thenReturn(Set.of(usuarioSeccion));

        when(seccionRepository.buscarSeccionesPorUsuario("usuario.test")).thenReturn(List.of(seccion));

        // Simular que el radicado se genera y se expide correctamente
        Radicado radicado = mock(Radicado.class);
        when(radicadoRepository.save(any())).thenReturn(radicado);
        when(radicadoMapper.toDTO(any())).thenReturn(mock(RadicadoDTO.class));

        // Ejecutar y verificar que no lanza excepción
        assertDoesNotThrow(() -> useCase.execute(command));
    }

    @Test
    @DisplayName("Debe lanzar excepción si el documento no existe")
    void deberiaLanzarExcepcionSiDocumentoNoExiste() {
        UUID documentoId = UUID.randomUUID();
        UUID destinatarioId = UUID.randomUUID();
        RadicarDocumentoEnvioCommand command = new RadicarDocumentoEnvioCommand(documentoId, destinatarioId, "usuario.test", List.of(), null);
        when(documentoRepository.findById(documentoId)).thenReturn(Optional.empty());

        assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
    }

    @Test
    @DisplayName("Debe lanzar excepción si el destinatario no existe")
    void deberiaLanzarExcepcionSiDestinatarioNoExiste() {
        UUID documentoId = UUID.randomUUID();
        UUID destinatarioId = UUID.randomUUID();
        RadicarDocumentoEnvioCommand command = new RadicarDocumentoEnvioCommand(documentoId, destinatarioId, "usuario.test", List.of(), null);
        Documento documento = mock(Documento.class);
        when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documento));
        when(consecutivoRepository.findByExample(any())).thenReturn(Optional.of(mock(Consecutivo.class)));
        when(externalUsersRepository.findById(destinatarioId)).thenReturn(Optional.empty());

        assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
    }

    private DefinicionMetadato mockDefinicionMetadato(String nombre) {
        // El nombre es usado como patrón en la lógica de validación
        return new DefinicionMetadato(nombre, TipoMetadatoEnum.CONTENIDO, FormatoMetadatoEnum.ALFANUMERICO);
    }

    @Test
    @DisplayName("Debe procesar correctamente las propiedades del radicado")
    void debeProcegarPropiedadesRadicadoCorrectamente() {
        // Arrange
        UUID documentId = UUID.randomUUID();
        UUID destinatarioId = UUID.randomUUID();
        String emisorUsername = "jmarin";

        PropiedadRadicadoDTO propiedadesRadicado = new PropiedadRadicadoDTO(
            131.0, 1.0, 489.0, 74.0, 189.0, 0.0
        );

        RadicarDocumentoEnvioCommand command = new RadicarDocumentoEnvioCommand(
            documentId,
            destinatarioId,
            emisorUsername,
            List.of(),
            propiedadesRadicado
        );

        // Mock dependencies
        Documento documento = mock(Documento.class);
        when(documento.getRadicados()).thenReturn(List.of());
        when(documento.getMetadatos()).thenReturn(Set.of());
        when(documentoRepository.findById(documentId)).thenReturn(Optional.of(documento));

        Consecutivo consecutivo = mock(Consecutivo.class);
        when(consecutivoRepository.findByExample(any())).thenReturn(Optional.of(consecutivo));

        ExternalUser destinatario = mock(ExternalUser.class);
        when(destinatario.getStatus()).thenReturn(ExternalUserStatus.ACTIVO);
        when(externalUsersRepository.findById(destinatarioId)).thenReturn(Optional.of(destinatario));

        UsuarioSeccion usuarioSeccion = mock(UsuarioSeccion.class);
        when(usuarioSeccion.getUsername()).thenReturn(emisorUsername);
        when(usuarioSeccion.getRelacion()).thenReturn(TipoRelacionUsuarioSeccion.PRIMARIA);

        co.com.gedsys.base.domain.organizacion.Seccion seccion = mock(co.com.gedsys.base.domain.organizacion.Seccion.class);
        when(seccion.getUsuarios()).thenReturn(Set.of(usuarioSeccion));
        when(seccionRepository.buscarSeccionesPorUsuario(emisorUsername)).thenReturn(List.of(seccion));

        when(definicionMetadatosRepository.buscarPorPatrones(any())).thenReturn(Set.of());

        Radicado radicadoGuardado = mock(Radicado.class);
        when(radicadoRepository.save(any(Radicado.class))).thenReturn(radicadoGuardado);

        RadicadoDTO radicadoDTO = mock(RadicadoDTO.class);
        when(radicadoMapper.toDTO(radicadoGuardado)).thenReturn(radicadoDTO);

        // Act
        RadicadoDTO resultado = useCase.execute(command);

        // Assert
        assertNotNull(resultado);

        // Verificar que se guardó un radicado con las propiedades correctas
        verify(radicadoRepository).save(argThat(radicado -> {
            if (radicado.getPropiedadesRadicado() == null) {
                return false;
            }
            var props = radicado.getPropiedadesRadicado();
            return props.getPage().equals(1.0) &&
                   props.getX().equals(74.0) &&
                   props.getY().equals(189.0) &&
                   props.getHeight().equals(131.0) &&
                   props.getWidth().equals(489.0) &&
                   props.getRotationDegrees().equals(0.0);
        }));
    }

}
