package co.com.gedsys.base.application.dto;

public record RadicadoDTO(
        String id,
        Integer numeroRadicado,
        String emisor,
        String fechaExpedicion,
        String observaciones,
        String prefijo,
        String sufijo,
        String tipo,
        ConsecutivoDS consecutivo,
        DocumentoDS documento,
        PropiedadesRadicadoDS propiedadesRadicado,
        ExternalUserDS destinatario,
        ExternalUserDS remitente,
        SeccionDS destino,
        String destinatarioInterno,
        String remitenteInterno
) {
    public record ConsecutivoDS(
            String id,
            Integer contador,
            String prefijo,
            String sufijo,
            String tipo,
            String tipoDocumentalId
    ) {
    }

    public record DocumentoDS(
            String id,
            String titulo,
            String fileId,
            String tipoDocumentalId,
            String clasificacionDocumentalId,
            String unidadDocumentalId
    ) {
    }

    public record PropiedadesRadicadoDS(
        Double page,
        Double x,
        Double y,
        Double height,
        Double width,
        Double rotationDegrees
    ) {
    }

    public record ExternalUserDS(
        String id,
        String name,
        String identificationType,
        String identificationNumber,
        String status
    ) {
    }

    public record SeccionDS(
        String id,
        String codigo,
        String nombre,
        String responsable,
        String tipo,
        String estado
    ) {
    }

    public record UsuarioSeccionDS(
        String id,
        String username,
        String relacion,
        SeccionDS seccion
    ) {
    }
}
