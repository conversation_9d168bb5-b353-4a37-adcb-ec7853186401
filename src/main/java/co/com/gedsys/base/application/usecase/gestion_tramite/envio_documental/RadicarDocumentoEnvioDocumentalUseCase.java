package co.com.gedsys.base.application.usecase.gestion_tramite.envio_documental;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.MetadatoDocumentoDTO;
import co.com.gedsys.base.application.dto.PropiedadRadicadoDTO;
import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.mapper.RadicadoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.gestion_tramite.envio_documental.RadicarDocumentoEnvioCommand;
import co.com.gedsys.base.application.usecase.gestion_tramite.envio_documental.exception.MetadatoNoExisteException;
import co.com.gedsys.base.application.usecase.gestion_tramite.envio_documental.exception.PoliticaUnicoRadicadoDeEnvioException;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.metadato.Metadato;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.metadato.DefinicionMetadato;
import co.com.gedsys.base.domain.radicado.EstadoRadicado;
import co.com.gedsys.base.domain.radicado.PropiedadesRadicado;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.radicado.RadicadoRepository;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import co.com.gedsys.base.domain.organizacion.UsuarioSeccion;
import co.com.gedsys.base.domain.organizacion.TipoRelacionUsuarioSeccion;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RadicarDocumentoEnvioDocumentalUseCase implements UseCase<RadicarDocumentoEnvioCommand, RadicadoDTO> {

    private final DocumentoRepository documentRepository;
    private final ConsecutivoRepository consecutivoRepository;
    private final RadicadoRepository radicadoRepository;
    private final ExternalUsersRepository externalUsersRepository;
    private final DefinicionMetadatosRepository definicionMetadatosRepository;
    private final SeccionRepository seccionRepository;
    @Qualifier("radicadoApplicationLayerMapperImpl")
    private final RadicadoApplicationLayerMapper radicadoMapper;

    @Transactional
    @Override
    public RadicadoDTO execute(RadicarDocumentoEnvioCommand command) {
        Documento documento = documentRepository.findById(command.documentId())
                .orElseThrow(() -> new EntityNotExistsException(
                        "No se encontró el documento con id " + command.documentId()));

        // Validar política de único radicado de envío activo
        boolean tieneRadicadoEnvioActivo = documento.getRadicados().stream()
                .anyMatch(r -> r.getTipo() == TipoConsecutivo.ENVIO && r.getEstado() == EstadoRadicado.ACTIVO);
        if (tieneRadicadoEnvioActivo) {
            throw new PoliticaUnicoRadicadoDeEnvioException();
        }

        Consecutivo consecutivo = obtenerConsecutivoEnvio();
        ExternalUser destinatario = externalUsersRepository.findById(command.destinatarioId())
                .orElseThrow(() -> new EntityNotExistsException(
                        "No se encontró el destinatario con id " + command.destinatarioId()));

        // NUEVO: Obtener remitente como UsuarioSeccion
        UsuarioSeccion remitente = obtenerRemitenteUsuarioSeccion(command.emisorUsername());

        List<String> nombresMetadatos = command.metadatos().stream().map(MetadatoDocumentoDTO::nombre).toList();
        Set<String> definidos = definicionMetadatosRepository.buscarPorPatrones(nombresMetadatos)
                .stream().map(DefinicionMetadato::getPatron).collect(Collectors.toSet());
        List<String> faltantes = nombresMetadatos.stream().filter(n -> !definidos.contains(n)).toList();
        if (!faltantes.isEmpty()) {
            throw new MetadatoNoExisteException("Faltan definiciones de metadatos: " + String.join(", ", faltantes));
        }

        // Agregar metadatos adicionales al documento
        Set<String> nombresActuales = documento.getMetadatos().stream().map(Metadato::nombre)
                .collect(Collectors.toSet());
        Map<String, String> valoresComando = command.metadatos().stream()
                .collect(Collectors.toMap(MetadatoDocumentoDTO::nombre, MetadatoDocumentoDTO::valor));
        Set<DefinicionMetadato> definiciones = definicionMetadatosRepository.buscarPorPatrones(nombresMetadatos);
        for (DefinicionMetadato definicion : definiciones) {
            String nombre = definicion.getPatron();
            if (!nombresActuales.contains(nombre)) {
                String valor = valoresComando.get(nombre);
                Metadato nuevoMetadato = definicion.generarMetadato(valor);
                documento.setMetadatos(List.of(nuevoMetadato));
            }
        }

        // NUEVO: Usar factory method específico para envíos
        Radicado radicado = Radicado.forEnvio(consecutivo, remitente.getUsername(), destinatario);

        // NUEVO: Procesar propiedades del radicado si están presentes
        if (command.propiedadRadicado() != null) {
            PropiedadRadicadoDTO propiedadDTO = command.propiedadRadicado();
            PropiedadesRadicado propiedades = new PropiedadesRadicado(
                    (double) propiedadDTO.page(),
                    (double) propiedadDTO.x(),
                    (double) propiedadDTO.y(),
                    (double) propiedadDTO.height(),
                    (double) propiedadDTO.width(),
                    (double) propiedadDTO.rotationDegrees());
            radicado.setPropiedadesRadicado(propiedades);
        }

        // MODIFICAR: usar nuevo método con emisorUsername
        radicado.expedirConEmisor(documento, command.emisorUsername());

        var radicadoGuardado = radicadoRepository.save(radicado);
        return radicadoMapper.toDTO(radicadoGuardado);
    }
    private Consecutivo obtenerConsecutivoEnvio() {
        Consecutivo consecutivoEjemplo = new Consecutivo(
                "ENV",
                String.valueOf(Calendar.getInstance().get(Calendar.YEAR)),
                0,
                co.com.gedsys.base.domain.consecutivo.TipoConsecutivo.ENVIO);
        return consecutivoRepository.findByExample(consecutivoEjemplo)
                .orElseGet(() -> {
                    Consecutivo nuevoConsecutivo = new Consecutivo(
                            "ENV",
                            String.valueOf(Calendar.getInstance().get(Calendar.YEAR)),
                            0,
                            co.com.gedsys.base.domain.consecutivo.TipoConsecutivo.ENVIO);
                    return consecutivoRepository.save(nuevoConsecutivo);
                });
    }

    private UsuarioSeccion obtenerRemitenteUsuarioSeccion(String username) {
        List<Seccion> secciones = seccionRepository.buscarSeccionesPorUsuario(username);

        return secciones.stream()
                .flatMap(seccion -> seccion.getUsuarios().stream())
                .filter(usuarioSeccion -> usuarioSeccion.getUsername().equals(username) &&
                        usuarioSeccion.getRelacion() == TipoRelacionUsuarioSeccion.PRIMARIA)
                .findFirst()
                .orElseThrow(() -> new EntityNotExistsException(
                        "Usuario " + username + " no tiene sección primaria asignada"));
    }

}
