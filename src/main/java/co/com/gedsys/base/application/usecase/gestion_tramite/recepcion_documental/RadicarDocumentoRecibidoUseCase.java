package co.com.gedsys.base.application.usecase.gestion_tramite.recepcion_documental;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.AnexoDocumentoDTO;
import co.com.gedsys.base.application.dto.MetadatoDocumentoDTO;
import co.com.gedsys.base.application.dto.PropiedadRadicadoDTO;
import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.mapper.RadicadoApplicationLayerMapper;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.UsuarioSeccion;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import co.com.gedsys.base.domain.documento.Anexo;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.metadato.Metadato;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.radicado.PropiedadesRadicado;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.radicado.RadicadoRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumentalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RadicarDocumentoRecibidoUseCase implements UseCase<RadicarDocumentoRecibidoCommand, RadicadoDTO> {

        private final DocumentoRepository documentoRepository;
        private final TipoDocumentalRepository tipoDocumentalRepository;
        private final UnidadDocumentalRepository unidadDocumentalRepository;
        private final DefinicionMetadatosRepository definicionMetadatosRepository;
        private final ConsecutivoRepository consecutivoRepository;
        private final RadicadoRepository radicadoRepository;
        private final RadicadoApplicationLayerMapper radicadoMapper;
        private final ExternalUsersRepository externalUsersRepository;
        private final ClasificacionDocumentalRepository clasificacionDocumentalRepository;

        @Override
        @Transactional
        public RadicadoDTO execute(RadicarDocumentoRecibidoCommand input) {
                Documento documento = crearDocumento(input);
                Documento documentoGuardado = documentoRepository.save(documento);

                ExternalUser remitente = obtenerRemitente(input.remitenteId());
                Seccion destino = obtenerDestino(documento.getUnidadDocumental());
                String destinatarioInterno = obtenerDestinatarioInterno(input.destinatarioInterno(), destino);

                Consecutivo consecutivo = obtenerConsecutivoRecepcion();

                Radicado radicado = Radicado.forRecepcion(consecutivo, remitente, destino, destinatarioInterno);

                if (input.propiedadRadicado() != null) {
                        PropiedadRadicadoDTO propiedadDTO = input.propiedadRadicado();
                        PropiedadesRadicado propiedades = new PropiedadesRadicado(
                                        (double) propiedadDTO.page(),
                                        (double) propiedadDTO.x(),
                                        (double) propiedadDTO.y(),
                                        (double) propiedadDTO.height(),
                                        (double) propiedadDTO.width(),
                                        (double) propiedadDTO.rotationDegrees());
                        radicado.setPropiedadesRadicado(propiedades);
                }

                radicado.validarCorrespondenciaParaRecepcion();

                radicado.expedir(documentoGuardado);

                consecutivoRepository.save(consecutivo);
                Radicado radicadoGuardado = radicadoRepository.save(radicado);

                return radicadoMapper.toDTO(radicadoGuardado);
        }

        private Documento crearDocumento(RadicarDocumentoRecibidoCommand input) {
                TipoDocumental tipoDocumental = tipoDocumentalRepository
                                .findById(UUID.fromString(input.tipoDocumentalId()))
                                .orElseThrow(() -> new EntityNotExistsException("Tipo documental no encontrado"));

                UnidadDocumental unidadDocumental = unidadDocumentalRepository
                                .findById(UUID.fromString(input.unidadDocumentalId()))
                                .orElseThrow(() -> new EntityNotExistsException("Unidad documental no encontrada"));

                Set<Metadato> metadatos = buildMetadatos(input.metadatos());

                List<Anexo> anexos = input.anexos().stream()
                                .map(this::buildAnexo)
                                .toList();

                return new Documento(
                                input.titulo(),
                                input.fileId(),
                                tipoDocumental,
                                unidadDocumental,
                                input.autor(),
                                metadatos,
                                Collections.emptyList(),
                                Collections.emptyList(),
                                anexos);
        }

        private Set<Metadato> buildMetadatos(List<MetadatoDocumentoDTO> metadatosDTO) {
                if (metadatosDTO == null || metadatosDTO.isEmpty()) {
                        return Collections.emptySet();
                }

                Map<String, String> mapaMetadatos = metadatosDTO.stream()
                                .collect(Collectors.toMap(
                                                MetadatoDocumentoDTO::nombre,
                                                MetadatoDocumentoDTO::valor));

                var definiciones = definicionMetadatosRepository.buscarPorPatrones(
                                new ArrayList<>(mapaMetadatos.keySet()));

                return definiciones.stream()
                                .map(d -> d.generarMetadato(mapaMetadatos.get(d.getPatron())))
                                .collect(Collectors.toSet());
        }

        private Anexo buildAnexo(AnexoDocumentoDTO anexoDTO) {
                return Anexo.builder()
                                .nombre(anexoDTO.nombre())
                                .descripcion(anexoDTO.descripcion())
                                .fileId(anexoDTO.fileId())
                                .hash(anexoDTO.hash())
                                .bytes(anexoDTO.bytes())
                                .extension(anexoDTO.extension())
                                .build();
        }
        private Consecutivo obtenerConsecutivoRecepcion() {
                String prefijo = "REC";
                String anioActual = String.valueOf(Calendar.getInstance().get(Calendar.YEAR));
                int contadorInicial = 0;

                Consecutivo consecutivoEjemplo = new Consecutivo(
                                prefijo,
                                anioActual, 
                                contadorInicial,
                                TipoConsecutivo.RECEPCION);

                return consecutivoRepository.findByExample(consecutivoEjemplo)
                                .orElseGet(() -> consecutivoRepository.save(consecutivoEjemplo));
        }

        private ExternalUser obtenerRemitente(String remitenteId) {
                if (remitenteId == null) {
                        throw new IllegalArgumentException("El remitente es requerido");
                }
                return externalUsersRepository.findById(UUID.fromString(remitenteId))
                                .orElseThrow(() -> new EntityNotExistsException("Remitente no encontrado"));
        }

        private Seccion obtenerDestino(UnidadDocumental unidadDocumental) {
                if (unidadDocumental == null) {
                        throw new EntityNotExistsException("La unidad documental es requerida para determinar el destino");
                }

                ClasificacionDocumental clasificacion = unidadDocumental.getClasificacion();
                if (clasificacion == null) {
                        throw new EntityNotExistsException("La unidad documental debe tener una clasificación documental asociada");
                }

                UUID clasificacionId = clasificacion.getId();
                if (clasificacionId == null) {
                        throw new EntityNotExistsException("La clasificación documental debe tener un ID válido");
                }

                return clasificacionDocumentalRepository.buscarSeccionPorClasificacionId(clasificacionId)
                        .orElseThrow(() -> new EntityNotExistsException("No se encontró la sección asociada a la clasificación documental con ID: " + clasificacionId));
        }

        private String obtenerDestinatarioInterno(String destinatarioInterno, Seccion destino) {
                if (destino == null) {
                        throw new IllegalArgumentException("La sección destino es requerida");
                }

                String usuarioABuscar = destinatarioInterno != null ? destinatarioInterno
                                : destino.getResponsable();

                return destino.getUsuarios().stream()
                                .filter(u -> u.getUsername().equals(usuarioABuscar))
                                .findFirst()
                                .map(UsuarioSeccion::getUsername)
                                .orElseThrow(() -> new EntityNotExistsException(
                                                destinatarioInterno == null
                                                                ? "No se encontró el responsable de la sección destino"
                                                                : "Destinatario interno no encontrado en la sección destino"));
        }
}
