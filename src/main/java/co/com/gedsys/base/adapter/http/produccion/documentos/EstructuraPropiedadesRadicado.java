package co.com.gedsys.base.adapter.http.produccion.documentos;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

public record EstructuraPropiedadesRadicado(
        @NotNull(message = "La altura es requerida")
        @Min(value = 0, message = "La altura debe ser mayor o igual a 0")
        double height,
        @NotNull(message = "La página es requerida")
        @Min(value = 0, message = "La página debe ser mayor o igual a 0")
        double page,
        @NotNull(message = "El ancho es requerido")
        @Min(value = 0, message = "El ancho debe ser mayor o igual a 0")
        double width,
        @NotNull(message = "La posición X es requerida")
        @Min(value = 0, message = "La posición X debe ser mayor o igual a 0")
        double x,
        @NotNull(message = "La posición Y es requerida")
        @Min(value = 0, message = "La posición Y debe ser mayor o igual a 0")
        double y,
        @NotNull(message = "El ángulo de rotación es requerido")
        @Min(value = 0, message = "El ángulo de rotación debe ser mayor o igual a 0")
        double rotationDegrees
) {
}
