-- Migración Fase 7: <PERSON><PERSON>za final de la migración Integer → Double
-- Elimina columnas INTEGER antiguas y renombra columnas DECIMAL a nombres originales
-- Issue: Completar migración de Integer a Double para PropiedadesRadicado

-- Verificar que las nuevas columnas DECIMAL tienen datos antes de eliminar las INTEGER
-- Esta verificación es opcional pero recomendada para seguridad
DO $$
BEGIN
    -- Verificar que existen registros con datos en las nuevas columnas
    IF EXISTS (
        SELECT 1 FROM radicados 
        WHERE prop_page_decimal IS NOT NULL 
           OR prop_x_decimal IS NOT NULL 
           OR prop_y_decimal IS NOT NULL 
           OR prop_height_decimal IS NOT NULL 
           OR prop_width_decimal IS NOT NULL 
           OR prop_rotation_degrees_decimal IS NOT NULL
    ) THEN
        RAISE NOTICE 'Datos encontrados en columnas DECIMAL, procediendo con la limpieza...';
    ELSE
        RAISE NOTICE 'No se encontraron datos en columnas DECIMAL, pero continuando...';
    END IF;
END $$;

-- Eliminar columnas INTEGER originales
ALTER TABLE radicados 
DROP COLUMN IF EXISTS prop_page,
DROP COLUMN IF EXISTS prop_x,
DROP COLUMN IF EXISTS prop_y,
DROP COLUMN IF EXISTS prop_height,
DROP COLUMN IF EXISTS prop_width,
DROP COLUMN IF EXISTS prop_rotation_degrees;

-- Renombrar columnas DECIMAL a nombres originales
ALTER TABLE radicados 
RENAME COLUMN prop_page_decimal TO prop_page,
RENAME COLUMN prop_x_decimal TO prop_x,
RENAME COLUMN prop_y_decimal TO prop_y,
RENAME COLUMN prop_height_decimal TO prop_height,
RENAME COLUMN prop_width_decimal TO prop_width,
RENAME COLUMN prop_rotation_degrees_decimal TO prop_rotation_degrees;

-- Opcional: Agregar comentarios a las columnas para documentar el cambio
COMMENT ON COLUMN radicados.prop_page IS 'Número de página (DECIMAL para soportar valores decimales)';
COMMENT ON COLUMN radicados.prop_x IS 'Coordenada X (DECIMAL para soportar valores decimales)';
COMMENT ON COLUMN radicados.prop_y IS 'Coordenada Y (DECIMAL para soportar valores decimales)';
COMMENT ON COLUMN radicados.prop_height IS 'Altura (DECIMAL para soportar valores decimales)';
COMMENT ON COLUMN radicados.prop_width IS 'Ancho (DECIMAL para soportar valores decimales)';
COMMENT ON COLUMN radicados.prop_rotation_degrees IS 'Grados de rotación (DECIMAL para soportar valores decimales)';

-- Mensaje de confirmación
DO $$
BEGIN
    RAISE NOTICE 'Migración completada: PropiedadesRadicado ahora soporta valores decimales';
END $$;
