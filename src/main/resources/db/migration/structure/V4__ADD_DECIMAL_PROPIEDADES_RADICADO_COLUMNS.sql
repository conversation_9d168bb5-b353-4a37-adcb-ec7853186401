-- Migración Fase 1: Agregar columnas DECIMAL para PropiedadesRadicado
-- Mantiene compatibilidad con columnas INTEGER existentes durante la transición
-- Issue: Migración de Integer a Double para soportar valores decimales como 430.3445534

-- Agregar nuevas columnas DECIMAL con precisión suficiente para coordenadas
-- DECIMAL(10,6) permite hasta 4 dígitos enteros y 6 decimales (ej: 9999.123456)
ALTER TABLE radicados 
ADD COLUMN IF NOT EXISTS prop_page_decimal DECIMAL(10,6),
ADD COLUMN IF NOT EXISTS prop_x_decimal DECIMAL(10,6),
ADD COLUMN IF NOT EXISTS prop_y_decimal DECIMAL(10,6),
ADD COLUMN IF NOT EXISTS prop_height_decimal DECIMAL(10,6),
ADD COLUMN IF NOT EXISTS prop_width_decimal DECIMAL(10,6),
ADD COLUMN IF NOT EXISTS prop_rotation_degrees_decimal DECIMAL(10,6);

-- <PERSON><PERSON><PERSON> datos existentes de columnas INTEGER a nuevas columnas DECIMAL
-- Usar COALESCE para manejar valores NULL apropiadamente
UPDATE radicados SET 
    prop_page_decimal = COALESCE(prop_page::DECIMAL(10,6), 0),
    prop_x_decimal = COALESCE(prop_x::DECIMAL(10,6), 0),
    prop_y_decimal = COALESCE(prop_y::DECIMAL(10,6), 0),
    prop_height_decimal = COALESCE(prop_height::DECIMAL(10,6), 0),
    prop_width_decimal = COALESCE(prop_width::DECIMAL(10,6), 0),
    prop_rotation_degrees_decimal = COALESCE(prop_rotation_degrees::DECIMAL(10,6), 0)
WHERE prop_page IS NOT NULL 
   OR prop_x IS NOT NULL 
   OR prop_y IS NOT NULL 
   OR prop_height IS NOT NULL 
   OR prop_width IS NOT NULL 
   OR prop_rotation_degrees IS NOT NULL;

-- Crear índices para las nuevas columnas si es necesario para rendimiento
-- (Comentado por ahora, se puede habilitar si se detectan problemas de rendimiento)
-- CREATE INDEX IF NOT EXISTS idx_radicados_prop_page_decimal ON radicados(prop_page_decimal);
-- CREATE INDEX IF NOT EXISTS idx_radicados_prop_x_decimal ON radicados(prop_x_decimal);
-- CREATE INDEX IF NOT EXISTS idx_radicados_prop_y_decimal ON radicados(prop_y_decimal);
